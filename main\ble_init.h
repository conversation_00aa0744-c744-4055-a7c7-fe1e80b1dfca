/*
 * BLE Initialization Header
 * BLE controller and stack initialization
 */

#pragma once

#include "esp_err.h"

#ifdef __cplusplus
extern "C"
{
#endif

    /**
     * @brief 初始化BLE模块
     *
     * 该函数完成以下初始化步骤：
     * 1. 释放经典蓝牙内存
     * 2. 初始化BLE控制器
     * 3. 启用BLE控制器
     * 4. 初始化蓝牙协议栈
     * 5. 启用蓝牙协议栈
     * 6. 初始化GAP层
     * 7. 初始化GATT层
     *
     * @return
     *     - ESP_OK: 初始化成功
     *     - 其他: 初始化失败的错误码
     */
    esp_err_t ble_task(void);

#ifdef __cplusplus
}
#endif
