#include "i2c_ms583730b.h"

#define MS5837_ADDR 0x76
#define I2C_MASTER_SDA_IO GPIO_NUM_9
#define I2C_MASTER_SCL_IO GPIO_NUM_8

static const char *TAG = "MS5837";
static i2c_master_bus_handle_t bus_handle = NULL;

//
static esp_err_t i2c_master_init(void)
{
    if (bus_handle != NULL)
    {
        return ESP_OK; // Already initialized
    }

    i2c_master_bus_config_t bus_config = {
        .clk_source = I2C_CLK_SRC_DEFAULT,
        .scl_io_num = I2C_MASTER_SCL_IO,
        .sda_io_num = I2C_MASTER_SDA_IO,
        .glitch_ignore_cnt = 7,
        .flags.enable_internal_pullup = true,
    };

    esp_err_t ret = i2c_new_master_bus(&bus_config, &bus_handle);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to create I2C master bus: %s", esp_err_to_name(ret));
        return ret;
    }

    ESP_LOGI(TAG, "I2C master bus initialized");
    return ESP_OK;
}

esp_err_t ms5837_init(ms5837_t *sensor)
{
    if (sensor == NULL)
    {
        return ESP_ERR_INVALID_ARG;
    }

    // Initialize I2C master bus
    esp_err_t ret = i2c_master_init();
    if (ret != ESP_OK)
    {
        return ret;
    }

    ESP_LOGI(TAG, "Initializing MS5837 at address 0x%02X", MS5837_ADDR);

    // Configure device at fixed address 0x76
    i2c_device_config_t dev_cfg = {
        .dev_addr_length = I2C_ADDR_BIT_LEN_7,
        .device_address = MS5837_ADDR,
        .scl_speed_hz = 100000,
    };

    ret = i2c_master_bus_add_device(bus_handle, &dev_cfg, &sensor->dev_handle);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to add MS5837 device to I2C bus: %s", esp_err_to_name(ret));
        return ret;
    }

    // Test communication with a reset command
    ret = ms5837_reset(sensor);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to communicate with MS5837. Check hardware connections:");
        ESP_LOGE(TAG, "  - SDA connected to GPIO9");
        ESP_LOGE(TAG, "  - SCL connected to GPIO8");
        ESP_LOGE(TAG, "  - VCC connected to 3.3V");
        ESP_LOGE(TAG, "  - GND connected to ground");
        ESP_LOGE(TAG, "  - Pull-up resistors on SDA/SCL (if external sensor)");
        i2c_master_bus_rm_device(sensor->dev_handle);
        sensor->dev_handle = NULL;
        return ret;
    }

    ESP_LOGI(TAG, "MS5837 communication established successfully");

    // Wait for reset to complete - MS5837 needs more time after reset
    vTaskDelay(pdMS_TO_TICKS(100)); // 增加等待时间到100ms

    // Read PROM calibration coefficients with retry mechanism
    int retry_count = 3;
    for (int retry = 0; retry < retry_count; retry++)
    {
        ret = ms5837_read_prom(sensor);
        if (ret == ESP_OK)
        {
            break;
        }
        else
        {
            ESP_LOGW(TAG, "PROM read attempt %d failed: %s", retry + 1, esp_err_to_name(ret));
            if (retry < retry_count - 1)
            {
                vTaskDelay(pdMS_TO_TICKS(10));
            }
        }
    }

    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to read PROM after %d attempts: %s", retry_count, esp_err_to_name(ret));
        i2c_master_bus_rm_device(sensor->dev_handle);
        sensor->dev_handle = NULL;
        return ret;
    }

    // Verify CRC
    uint8_t crc_read = (sensor->prom[0] >> 12) & 0x0F;
    uint8_t crc_calc = ms5837_crc4(sensor->prom);

    if (crc_read != crc_calc)
    {
        ESP_LOGE(TAG, "CRC mismatch: read=0x%02X, calculated=0x%02X", crc_read, crc_calc);
        i2c_master_bus_rm_device(sensor->dev_handle);
        sensor->dev_handle = NULL;
        return ESP_ERR_INVALID_CRC;
    }

    sensor->initialized = true;
    ESP_LOGI(TAG, "MS5837 initialized successfully");

    // Print PROM coefficients for debugging
    ESP_LOGI(TAG, "PROM coefficients:");
    ESP_LOGI(TAG, "PROM[0] (CRC+Factory): 0x%04X", sensor->prom[0]);
    ESP_LOGI(TAG, "C1 (Pressure sensitivity): 0x%04X", sensor->prom[1]);
    ESP_LOGI(TAG, "C2 (Pressure offset): 0x%04X", sensor->prom[2]);
    ESP_LOGI(TAG, "C3 (Temp coeff of pressure sensitivity): 0x%04X", sensor->prom[3]);
    ESP_LOGI(TAG, "C4 (Temp coeff of pressure offset): 0x%04X", sensor->prom[4]);
    ESP_LOGI(TAG, "C5 (Reference temperature): 0x%04X", sensor->prom[5]);
    ESP_LOGI(TAG, "C6 (Temp coeff of temperature): 0x%04X", sensor->prom[6]);
    ESP_LOGI(TAG, "PROM[7] (Subsidiary): 0x%04X", sensor->prom[7]);

    return ESP_OK;
}

esp_err_t ms5837_reset(ms5837_t *sensor)
{
    if (sensor == NULL)
    {
        return ESP_ERR_INVALID_ARG;
    }

    uint8_t cmd = MS5837_CMD_RESET;
    esp_err_t ret = i2c_master_transmit(sensor->dev_handle, &cmd, 1, pdMS_TO_TICKS(1000));
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to send reset command: %s", esp_err_to_name(ret));
        return ret;
    }

    ESP_LOGI(TAG, "MS5837 reset command sent");
    return ESP_OK;
}

esp_err_t ms5837_read_prom(ms5837_t *sensor)
{
    if (sensor == NULL)
    {
        return ESP_ERR_INVALID_ARG;
    }

    ESP_LOGI(TAG, "Starting PROM read...");

    // Read only the physically available PROM addresses (0-6)
    for (int i = 0; i < MS5837_PROM_READ_SIZE; i++)
    {
        uint8_t cmd = MS5837_CMD_PROM_READ + (i * 2);
        uint8_t data[2];

        ESP_LOGI(TAG, "Reading PROM address %d, command: 0x%02X", i, cmd);

        esp_err_t ret = i2c_master_transmit_receive(sensor->dev_handle, &cmd, 1, data, 2, pdMS_TO_TICKS(1000));
        if (ret != ESP_OK)
        {
            ESP_LOGE(TAG, "Failed to read PROM address %d (cmd=0x%02X): %s", i, cmd, esp_err_to_name(ret));
            return ret;
        }

        sensor->prom[i] = (data[0] << 8) | data[1];
        ESP_LOGI(TAG, "PROM[%d] = 0x%04X (data: 0x%02X 0x%02X)", i, sensor->prom[i], data[0], data[1]);
    }

    // Set PROM[7] to 0 as required by CRC calculation (not physically readable)
    sensor->prom[7] = 0;
    ESP_LOGI(TAG, "PROM coefficients read successfully");
    return ESP_OK;
}

uint8_t ms5837_crc4(uint16_t n_prom[])
{
    int cnt; // simple counter
    unsigned int n_rem=0; // crc remainder
    unsigned char n_bit;
    n_prom[0]=((n_prom[0]) & 0x0FFF); // CRC byte is replaced by 0
    n_prom[7]=0; // Subsidiary value, set to 0
    for (cnt = 0; cnt < 16; cnt++) // operation is performed on bytes
    { // choose LSB or MSB
        if (cnt%2==1)
        { 
            n_rem ^= (unsigned short) ((n_prom[cnt>>1]) & 0x00FF);
        }
        else 
        {
            n_rem ^= (unsigned short) (n_prom[cnt>>1]>>8);
        }
        for (n_bit = 8; n_bit > 0; n_bit--)
        {
            if (n_rem & (0x8000)){
                n_rem = (n_rem << 1) ^ 0x3000;
            } 
            else {
                n_rem = (n_rem << 1);
            }
        }
    }
    n_rem= ((n_rem >> 12) & 0x000F); // final 4-bit remainder is CRC code
    return (n_rem ^ 0x00);
}

static esp_err_t ms5837_read_adc(ms5837_t *sensor, uint32_t *adc_value)
{
    if (sensor == NULL || adc_value == NULL)
    {
        return ESP_ERR_INVALID_ARG;
    }

    uint8_t cmd = MS5837_CMD_ADC_READ;
    uint8_t data[3];

    esp_err_t ret = i2c_master_transmit_receive(sensor->dev_handle, &cmd, 1, data, 3, pdMS_TO_TICKS(1000));
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to read ADC: %s", esp_err_to_name(ret));
        return ret;
    }

    *adc_value = (data[0] << 16) | (data[1] << 8) | data[2];
    return ESP_OK;
}

static esp_err_t ms5837_convert_d1(ms5837_t *sensor, uint32_t *d1)
{
    if (sensor == NULL || d1 == NULL)
    {
        return ESP_ERR_INVALID_ARG;
    }

    // Start pressure conversion (OSR=4096)
    uint8_t cmd = MS5837_CMD_CONV_D1_4096;
    esp_err_t ret = i2c_master_transmit(sensor->dev_handle, &cmd, 1, pdMS_TO_TICKS(1000));
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to start D1 conversion: %s", esp_err_to_name(ret));
        return ret;
    }

    // Wait for conversion to complete (OSR=4096 takes ~8.22ms)
    vTaskDelay(pdMS_TO_TICKS(10));

    // Read ADC result
    return ms5837_read_adc(sensor, d1);
}

static esp_err_t ms5837_convert_d2(ms5837_t *sensor, uint32_t *d2)
{
    if (sensor == NULL || d2 == NULL)
    {
        return ESP_ERR_INVALID_ARG;
    }

    // Start temperature conversion (OSR=4096)
    uint8_t cmd = MS5837_CMD_CONV_D2_4096;
    esp_err_t ret = i2c_master_transmit(sensor->dev_handle, &cmd, 1, pdMS_TO_TICKS(1000));
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "Failed to start D2 conversion: %s", esp_err_to_name(ret));
        return ret;
    }

    // Wait for conversion to complete (OSR=4096 takes ~8.22ms)
    vTaskDelay(pdMS_TO_TICKS(10));

    // Read ADC result
    return ms5837_read_adc(sensor, d2);
}

esp_err_t ms5837_get_data(ms5837_t *sensor, float *temperature, float *pressure)
{
    if (sensor == NULL || temperature == NULL || pressure == NULL)
    {
        return ESP_ERR_INVALID_ARG;
    }

    if (!sensor->initialized)
    {
        ESP_LOGE(TAG, "Sensor not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    uint32_t d1, d2;

    // Read raw pressure (D1)
    esp_err_t ret = ms5837_convert_d1(sensor, &d1);
    if (ret != ESP_OK)
    {
        return ret;
    }

    // Read raw temperature (D2)
    ret = ms5837_convert_d2(sensor, &d2);
    if (ret != ESP_OK)
    {
        return ret;
    }

    // Calculate temperature and pressure according to MS5837 datasheet
    // Extract calibration coefficients
    uint16_t c1 = sensor->prom[1]; // Pressure sensitivity
    uint16_t c2 = sensor->prom[2]; // Pressure offset
    uint16_t c3 = sensor->prom[3]; // Temperature coefficient of pressure sensitivity
    uint16_t c4 = sensor->prom[4]; // Temperature coefficient of pressure offset
    uint16_t c5 = sensor->prom[5]; // Reference temperature
    uint16_t c6 = sensor->prom[6]; // Temperature coefficient of the temperature

    // Calculate temperature
    int32_t dt = d2 - ((uint32_t)c5 << 8);
    int32_t temp = 2000 + (((int64_t)dt * c6) >> 23);

    // Calculate pressure
    int64_t off = ((int64_t)c2 << 16) + (((int64_t)c4 * dt) >> 7);
    int64_t sens = ((int64_t)c1 << 15) + (((int64_t)c3 * dt) >> 8);

    // Second order temperature compensation
    int32_t t2 = 0;
    int64_t off2 = 0;
    int64_t sens2 = 0;

    if (temp < 2000)
    {
        // Low temperature
        t2 = (3 * ((int64_t)dt * dt)) >> 33;
        off2 = (3 * (temp - 2000) * (temp - 2000)) >> 1;
        sens2 = (5 * (temp - 2000) * (temp - 2000)) >> 3;

        if (temp < -1500)
        {
            // Very low temperature
            off2 = off2 + 7 * (temp + 1500) * (temp + 1500);
            sens2 = sens2 + 4 * (temp + 1500) * (temp + 1500);
        }
    }
    else if (temp >= 4500)
    {
        // High temperature
        t2 = (2 * ((int64_t)dt * dt)) >> 37;
        off2 = ((temp - 4500) * (temp - 4500)) >> 4;
        sens2 = 0;
    }

    // Convert to float values
    *temperature = temp / 100.0f; // Temperature in °C

    // 使用浮点计算，避免溢出
    float d1_f = (float)d1;
    float sens_f = (float)sens;
    float off_f = (float)off;

    // 按公式计算 mbar
    float p_mbar = (((d1_f * sens_f) / 2097152.0f) - off_f) / 8192.0f / 10.0f;

    // mbar转换为kPa
    *pressure = p_mbar / 10.0f; 

    // ESP_LOGI(TAG, "Raw D1: %lu, D2: %lu", d1, d2);
     ESP_LOGI(TAG, "Temperature: %.2f deg , Pressure: %.2f kPa", *temperature, *pressure);
    return ESP_OK;
}