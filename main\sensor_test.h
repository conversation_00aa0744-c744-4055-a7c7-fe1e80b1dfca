#ifndef SENSOR_TEST_H
#define SENSOR_TEST_H

#include "esp_err.h"

#ifdef __cplusplus
extern "C"
{
#endif

/**
 * @brief 初始化传感器测试模块
 * @return ESP_OK 成功，其他值表示失败
 */
esp_err_t sensor_test_init(void);

/**
 * @brief 启动传感器测试任务
 * @return ESP_OK 成功，其他值表示失败
 */
esp_err_t sensor_test_start_task(void);

/**
 * @brief 停止传感器测试任务
 * @return ESP_OK 成功，其他值表示失败
 */
esp_err_t sensor_test_stop_task(void);

#ifdef __cplusplus
}
#endif

#endif // SENSOR_TEST_H
