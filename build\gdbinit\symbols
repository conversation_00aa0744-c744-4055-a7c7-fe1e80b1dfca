# Load esp32c3 ROM ELF symbols
define target hookpost-remote
set confirm off
  # if $_streq((char *) 0x3ff1b878, "Sep 18 2020")
  if (*(int*) 0x3ff1b878) == 0x20706553 && (*(int*) 0x3ff1b87c) == 0x32203831 && (*(int*) 0x3ff1b880) == 0x303230
    add-symbol-file e:/esp_tool/tools/esp-rom-elfs/20240305/esp32c3_rev0_rom.elf
  else
    # if $_streq((char *) 0x3ff1a374, "Feb  7 2021")
    if (*(int*) 0x3ff1a374) == 0x20626546 && (*(int*) 0x3ff1a378) == 0x32203720 && (*(int*) 0x3ff1a37c) == 0x313230
      add-symbol-file e:/esp_tool/tools/esp-rom-elfs/20240305/esp32c3_rev3_rom.elf
    else
      echo Warning: Unknown esp32c3 ROM revision.\n
    end
  end
set confirm on
end


# Load bootloader symbols
set confirm off
    add-symbol-file E:/esp32_space/ble_mill_monitor_slave/build/bootloader/bootloader.elf
set confirm on

# Load application symbols
file E:/esp32_space/ble_mill_monitor_slave/build/ble_slave.elf
