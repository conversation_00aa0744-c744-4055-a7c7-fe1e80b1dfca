/*
 * 软件版本定义
 * BLE Mill Monitor Slave - ESP32-C3
 */

#pragma once

#include <stdint.h>
#include "esp_err.h"

#ifdef __cplusplus
extern "C"
{
#endif

// 软件版本定义 (2字节格式)
#define SOFTWARE_VERSION_MAJOR 0 // 主版本号 (高字节)
#define SOFTWARE_VERSION_MINOR 8 // 小版本号 (低字节)

// 组合版本号(不分大小端, 主版本在前, 小版本在后)
#define SOFTWARE_VERSION_2BYTE ((SOFTWARE_VERSION_MAJOR << 4) | SOFTWARE_VERSION_MINOR)

    /**
     * @brief 打印简化的版本信息
     * 只显示: 设备ID, 设备MAC, 软件版本(2字节)
     */
    void print_version_info(void);

    /**
     * @brief 获取2字节格式的软件版本
     * @return 2字节版本号 (高字节=主版本, 低字节=小版本)
     */
    esp_err_t get_software_ver(uint8_t *ver);

#ifdef __cplusplus
}
#endif
