/**
 ******************************************************************************
 * @file           : ntc_convert.h
 * @brief          : Header for ntc_convert.c file - NTC temperature conversion
 ******************************************************************************
 * @attention
 *
 * NTC温度转换模块
 * 支持多种电路接线方式和温度计算算法
 *
 ******************************************************************************
 */

#pragma once

#include "sdkconfig.h"
#include "esp_err.h"
#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C"
{
#endif

#define NTC_T_OFFSET 200 // 温度偏置 +20℃ (x10)

    /**
     * @brief NTC电路接线类型
     */
    typedef enum
    {
        NTC_TYPE_PULLUP_SAME_REF = 0,   // 上拉电阻，Vref = Vs (最常用)
        NTC_TYPE_PULLUP_DIFF_REF = 1,   // 上拉电阻，Vref ≠ Vs
        NTC_TYPE_PULLDOWN_SAME_REF = 2, // 下拉电阻，Vref = Vs
        NTC_TYPE_PULLDOWN_DIFF_REF = 3  // 下拉电阻，Vref ≠ Vs
    } ntc_circuit_type_t;

    /**
     * @brief NTC配置结构体
     */
    typedef struct
    {
        ntc_circuit_type_t type; // 接线类型
        uint16_t max_adc;        // ADC最大值 (通常4095 for 12-bit)
        uint16_t rs;             // 串联电阻值 (Ω)
        uint16_t r25;            // 25℃时的NTC阻值 (Ω)
        uint16_t b25;            // NTC的B25值常数
        float vs;                // 分压网络电源电压 (V)
        float vref;              // ADC参考电压 (V)
    } ntc_config_t;

    /**
     * @brief NTC运行时数据结构体
     */
    typedef struct
    {
        ntc_config_t config; // 配置参数
        uint16_t rntc;       // 当前NTC阻值 (Ω)
        float temp_kelvin;   // 开氏温度 (K)
        float temp_celsius;  // 摄氏温度 (℃)
        bool initialized;    // 初始化标志
    } ntc_handle_t;

    /**
     * @brief 初始化NTC转换器
     * @param handle NTC句柄
     * @param config NTC配置参数
     * @return ESP_OK 成功, ESP_ERR_INVALID_ARG 参数错误
     */
    esp_err_t ntc_convert_init(ntc_handle_t *handle, const ntc_config_t *config);

    /**
     * @brief 根据ADC值转换为NTC温度 (整数版本)
     * @param handle NTC句柄
     * @param adc_value ADC原始值
     * @param temp_int 输出温度整数值 (摄氏温度 x 10 + 偏置)
     * @return ESP_OK 成功, ESP_ERR_INVALID_ARG 参数错误, ESP_ERR_INVALID_STATE 未初始化
     */
    esp_err_t ntc_convert_temperature(ntc_handle_t *handle, uint16_t adc_value, int16_t *temp_int);

    /**
     * @brief 获取默认NTC配置
     * @param config 配置结构体指针
     * @return ESP_OK 成功
     */
    esp_err_t ntc_convert_default_config(ntc_config_t *config);

#ifdef __cplusplus
}
#endif
