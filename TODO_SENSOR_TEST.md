# 传感器测试任务实施完成

## 已完成的工作

### 1. 创建传感器测试模块
- ✅ `main/sensor_test.h` - 传感器测试模块头文件
- ✅ `main/sensor_test.c` - 传感器测试模块实现
  - 创建FreeRTOS任务，每秒执行一次
  - 调用MS5837传感器读取压力数据
  - 格式化打印压力值（kPa）和温度值（°C）
  - 包含完整的错误处理

### 2. 启用真实传感器功能
- ✅ 修改 `main/ble_gatt.c` 中的 `get_pressure()` 函数
  - 取消注释真实的MS5837传感器读取代码
  - 启用MS5837传感器初始化
  - 将日志级别从LOGW改为LOGD以减少输出

### 3. 集成到主程序
- ✅ 修改 `main/main.c`
  - 添加sensor_test.h头文件包含
  - 在LED充电初始化后添加传感器测试模块初始化
  - 启动传感器测试任务

### 4. 更新构建配置
- ✅ 修改 `main/CMakeLists.txt`
  - 添加sensor_test.c到源文件列表

## 功能特性

1. **每秒打印压力数据**: 任务每1000ms执行一次，读取并打印MS5837压力传感器数据
2. **完整错误处理**: 包含传感器初始化失败和读取失败的错误处理
3. **格式化输出**: 压力显示为kPa单位，温度显示为°C单位
4. **任务管理**: 支持启动和停止测试任务
5. **资源管理**: 合理的任务栈大小(4096字节)和优先级(5)

## 预期输出

系统启动后，串口监视器将每秒显示类似以下的日志：
```
I (xxxx) SENSOR_TEST: MS5837 压力: 101.32 kPa, 温度: 25.60°C
```

如果传感器读取失败，将显示错误信息：
```
E (xxxx) SENSOR_TEST: MS5837 读取失败: [错误代码]
```

## 注意事项

1. 确保I2C总线正确连接MS5837传感器
2. 检查传感器电源供应是否正常
3. 如果看到初始化失败，检查I2C引脚配置
4. 任务优先级设置为5，不会影响其他系统任务

## 测试验证

编译并烧录固件后：
1. 打开串口监视器
2. 观察系统启动日志
3. 确认看到"传感器测试任务已启动"消息
4. 每秒应该看到压力数据输出
5. 验证压力值是否在合理范围内（通常大气压约101.3kPa）

任务完成！✅
