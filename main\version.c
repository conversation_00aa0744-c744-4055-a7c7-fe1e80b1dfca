/*
 * 软件版本信息实现
 */

#include <stdio.h>
#include "esp_log.h"
#include "version.h"
#include "dev_id.h"

static const char *TAG = "VERSION";

void print_version_info(void)
{
    uint8_t device_id = 0;
    uint8_t device_mac[6] = {0};

    ESP_LOGI(TAG, "========================================");
    ESP_LOGI(TAG, "        Device Info");
    ESP_LOGI(TAG, "========================================");

    // 获取并打印设备ID
    esp_err_t ret = get_device_id(&device_id);
    if (ret == ESP_OK)
    {
        ESP_LOGI(TAG, "device id: %d", device_id);
    }
    else
    {
        ESP_LOGW(TAG, "get device id failed: %s", esp_err_to_name(ret));
    }

    // 获取并打印设备MAC地址
    ret = get_device_mac(device_mac);
    if (ret == ESP_OK)
    {
        ESP_LOGI(TAG, "device mac: %02X:%02X:%02X:%02X:%02X:%02X",
                 device_mac[0], device_mac[1], device_mac[2],
                 device_mac[3], device_mac[4], device_mac[5]);
    }
    else
    {
        ESP_LOGW(TAG, "get device mac failed: %s", esp_err_to_name(ret));
    }

    // 打印软件版本 (2字节格式)
    ESP_LOGI(TAG, "software ver: (0x%04X)", SOFTWARE_VERSION_2BYTE);

    ESP_LOGI(TAG, "========================================");
}

esp_err_t get_software_ver(uint8_t *ver)
{
    *ver = SOFTWARE_VERSION_2BYTE;
    return ESP_OK;
}
