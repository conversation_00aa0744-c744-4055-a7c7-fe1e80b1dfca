/*
 * Device LED Control Header
 * LED GPIO control and blink functionality
 * Integrated OTA control based on power source
 */

#pragma once

#include "esp_err.h"
#include "driver/gpio.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"

#ifdef __cplusplus
extern "C"
{
#endif

    /* LED状态定义 */
    typedef enum
    {
        LED_STATE_OFF = 1, /* LED关闭 */
        LED_STATE_ON = 0   /* LED开启 */
    } led_state_t;

    /* 电源来源状态定义 */
    typedef enum
    {
        POWER_SOURCE_EXTERNAL = 0, /* 外部供电（低电平） */
        POWER_SOURCE_BATTERY = 1   /* 电池供电（高电平） */
    } power_source_t;

    /* 充电状态定义 */
    typedef enum
    {
        CHARGE_STATUS_COMPLETE = 0, /* 充电完成（低电平） */
        CHARGE_STATUS_CHARGING = 1  /* 充电中（高电平） */
    } charge_status_t;

    /**
     * @brief 初始化并创建LED充电监控任务（一键启动）
     *
     * 自动执行以下步骤：
     * 1. 如果未初始化，先调用led_charge_init()进行GPIO初始化
     * 2. 创建FreeRTOS任务进行LED控制和电源监控
     * 3. 根据电源状态自动启动/停止OTA服务
     *
     * @return
     *     - ESP_OK: 初始化和任务创建成功
     *     - 其他: 初始化失败或任务创建失败的错误码
     */
    esp_err_t led_charge_task();

    

#ifdef __cplusplus
}
#endif
