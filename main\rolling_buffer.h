#ifndef ROLLING_BUFFER_H
#define ROLLING_BUFFER_H

#include <stdbool.h>
#include <stdint.h>

#define MAX_NODES 20
#define NULL_INDEX -1

// 传感器数据结构
typedef struct
{
    int16_t temperature; // 温度 x 10 (支持负温度)
    uint16_t pressure;   // 压力 x 10
} sensor_data_t;

typedef struct
{
    sensor_data_t data; // 存储的传感器数据
    int next;           // 下一个元素索引
    int prev;           // 上一个元素索引
    bool used;          // 是否被占用
} rb_node;

void rb_init(void);
void rb_append(sensor_data_t *data);               // 添加新的传感器数据，满了会滚动
void rb_remove_head(void);                         // 删除头部节点
void rb_print(void);                               // 遍历打印所有节点
int rb_count(void);                                // 当前元素数量
void rb_clear(void);                               // 清空整个缓冲区
int rb_get_latest_n(sensor_data_t *output, int n); // 获取最新的N组数据

#endif // ROLLING_BUFFER_H
