/**
 ******************************************************************************
 * @file           : ntc_convert.c
 * @brief          : NTC temperature conversion implementation
 ******************************************************************************
 * @attention
 *
 * NTC温度转换模块实现
 * 使用简化的Steinhart-Hart公式进行温度计算
 *
 * 温度计算公式：
 * 1/T = 1/T25 + 1/B * ln(R / R25)
 * 其中：T为绝对温度(K), T25=298.15K, 1/T25=0.003354
 *
 ******************************************************************************
 */

#include "ntc_convert.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include "esp_log.h"

static const char *TAG = "NTC_CONVERT";

// 温度计算常数
#define T25_KELVIN 298.15f        // 25℃对应的开氏温度
#define INV_T25 0.003354f         // 1/T25
#define CELSIUS_TO_KELVIN 273.15f // 摄氏度转开氏度偏移

static esp_err_t ntc_calculate_resistance(ntc_handle_t *handle, uint16_t adc_value);
static esp_err_t ntc_calculate_temperature(ntc_handle_t *handle);

esp_err_t ntc_convert_default_config(ntc_config_t *config)
{
    if (config == NULL)
    {
        return ESP_ERR_INVALID_ARG;
    }

    // 设置默认配置 - 参考原ntc.c的默认值
    config->type = NTC_TYPE_PULLUP_SAME_REF; // 上拉电阻，Vref = Vs
    config->max_adc = 4095;                  // 12位ADC
    config->rs = 10000;                      // 10kΩ上拉电阻
    config->r25 = 10000;                     // 25℃时10kΩ
    config->b25 = 3950;                      // B25值
    config->vs = 3.3f;                       // 3.3V电源 (修改为ESP32常用电压)
    config->vref = 3.3f;                     // 3.3V ADC参考电压

    return ESP_OK;
}

esp_err_t ntc_convert_init(ntc_handle_t *handle, const ntc_config_t *config)
{
    if (handle == NULL || config == NULL)
    {
        ESP_LOGE(TAG, "Invalid arguments");
        return ESP_ERR_INVALID_ARG;
    }

    // 参数有效性检查
    if (config->max_adc == 0 || config->rs == 0 || config->r25 == 0 ||
        config->b25 == 0 || config->vs <= 0 || config->vref <= 0)
    {
        ESP_LOGE(TAG, "Invalid configuration parameters");
        return ESP_ERR_INVALID_ARG;
    }

    // 复制配置
    memcpy(&handle->config, config, sizeof(ntc_config_t));

    // 初始化运行时数据
    handle->rntc = 0;
    handle->temp_kelvin = 0.0f;
    handle->temp_celsius = 0.0f;
    handle->initialized = true;

    return ESP_OK;
}

esp_err_t ntc_convert_temperature(ntc_handle_t *handle, uint16_t adc_value, int16_t *temp_int)
{
    if (handle == NULL || temp_int == NULL)
    {
        ESP_LOGE(TAG, "Invalid arguments");
        return ESP_ERR_INVALID_ARG;
    }

    if (!handle->initialized)
    {
        ESP_LOGE(TAG, "NTC converter not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    // 计算NTC阻值
    esp_err_t ret = ntc_calculate_resistance(handle, adc_value);
    if (ret != ESP_OK)
    {
        return ret;
    }

    // 计算温度
    ret = ntc_calculate_temperature(handle);
    if (ret != ESP_OK)
    {
        return ret;
    }

    // 转换为整型 x 10 且偏置 +20℃ (参考原ntc.c的返回格式)
    *temp_int = (int16_t)(handle->temp_celsius * 10 + NTC_T_OFFSET);

    ESP_LOGI(TAG, "ADC:%d -> R:%dΩ -> T:%.2f℃ -> INT:%d", adc_value, handle->rntc, handle->temp_celsius, *temp_int);

    return ESP_OK;
}

static esp_err_t ntc_calculate_resistance(ntc_handle_t *handle, uint16_t adc_value)
{
    float temp_f0, temp_f1, temp_f2;
    uint32_t temp_d1, temp_d2;

    // 防止除零错误
    if (adc_value >= handle->config.max_adc)
    {
        ESP_LOGW(TAG, "ADC value %d >= max_adc %d", adc_value, handle->config.max_adc);
        adc_value = handle->config.max_adc - 1;
    }

    switch (handle->config.type)
    {
    case NTC_TYPE_PULLUP_SAME_REF: // 接法0：上拉电阻，Vref = Vs
        // NTC阻值 = AdcVal * Rs / (MaxAdc - AdcVal)
        temp_d1 = adc_value;
        temp_d1 *= handle->config.rs;
        temp_d2 = handle->config.max_adc - adc_value;
        if (temp_d2 == 0)
        {
            ESP_LOGE(TAG, "Division by zero in resistance calculation");
            return ESP_ERR_INVALID_ARG;
        }
        handle->rntc = temp_d1 / temp_d2;
        break;

    case NTC_TYPE_PULLUP_DIFF_REF: // 接法1：上拉电阻，Vref ≠ Vs
        // NTC阻值 = Vref * AdcVal * Rs / (Vs * MaxAdc - Vref * AdcVal)
        temp_f0 = (float)adc_value;
        temp_f0 *= handle->config.vref;
        temp_f1 = temp_f0 * handle->config.rs;
        temp_f2 = (float)handle->config.max_adc;
        temp_f2 *= handle->config.vs;
        temp_f2 -= temp_f0;
        if (temp_f2 <= 0)
        {
            ESP_LOGE(TAG, "Invalid denominator in resistance calculation");
            return ESP_ERR_INVALID_ARG;
        }
        handle->rntc = (uint16_t)(temp_f1 / temp_f2);
        break;

    case NTC_TYPE_PULLDOWN_SAME_REF: // 接法2：下拉电阻，Vref = Vs
        // NTC阻值 = Rs * (MaxAdc - AdcVal) / AdcVal
        temp_d1 = handle->config.max_adc - adc_value;
        temp_d1 *= handle->config.rs;
        temp_d2 = adc_value;
        if (temp_d2 == 0)
        {
            ESP_LOGE(TAG, "Division by zero in resistance calculation");
            return ESP_ERR_INVALID_ARG;
        }
        handle->rntc = temp_d1 / temp_d2;
        break;

    case NTC_TYPE_PULLDOWN_DIFF_REF: // 接法3：下拉电阻，Vref ≠ Vs
        // NTC阻值 = Rs * (Vs * MaxAdc - Vref * AdcVal) / (Vref * AdcVal)
        temp_f0 = (float)handle->config.max_adc;
        temp_f0 *= handle->config.vs; // Vs * MaxAdc
        temp_f1 = (float)adc_value;
        temp_f1 *= handle->config.vref; // Vref * AdcVal
        temp_f2 = temp_f0 - temp_f1;    // Vs * MaxAdc - Vref * AdcVal
        temp_f2 *= handle->config.rs;   // Rs * (Vs * MaxAdc - Vref * AdcVal)
        if (temp_f1 <= 0)
        {
            ESP_LOGE(TAG, "Invalid denominator in resistance calculation");
            return ESP_ERR_INVALID_ARG;
        }
        handle->rntc = (uint16_t)(temp_f2 / temp_f1);
        break;

    default:
        ESP_LOGE(TAG, "Invalid NTC circuit type: %d", handle->config.type);
        return ESP_ERR_INVALID_ARG;
    }

    return ESP_OK;
}

static esp_err_t ntc_calculate_temperature(ntc_handle_t *handle)
{
    // 使用简化的Steinhart-Hart公式计算温度
    // 1/T = 1/T25 + 1/B * ln(R / R25)

    float r_ratio = (float)handle->rntc / (float)handle->config.r25;
    if (r_ratio <= 0)
    {
        ESP_LOGE(TAG, "Invalid resistance ratio");
        return ESP_ERR_INVALID_ARG;
    }

    float ln_r_ratio = logf(r_ratio);                        // ln(R / R25)
    float inv_t = INV_T25 + ln_r_ratio / handle->config.b25; // 1/T25 + ln(R/R25)/B25

    if (inv_t <= 0)
    {
        ESP_LOGE(TAG, "Invalid temperature calculation");
        return ESP_ERR_INVALID_ARG;
    }

    handle->temp_kelvin = 1.0f / inv_t;                             // 开氏温度
    handle->temp_celsius = handle->temp_kelvin - CELSIUS_TO_KELVIN; // 摄氏温度

    return ESP_OK;
}
