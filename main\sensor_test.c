#include "sensor_test.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "i2c_ms583730b.h"

static const char *TAG = "SENSOR_TEST";

// Task handle
static TaskHandle_t sensor_test_task_handle = NULL;

// MS5837 sensor instance
static ms5837_t sensor;

// Sensor test task
static void sensor_test_task(void *pvParameters)
{
    ESP_LOGI(TAG, "Sensor test task started, printing pressure data every second");
    
    // 添加初始延迟，让传感器完全稳定
    vTaskDelay(pdMS_TO_TICKS(1500)); // 等待1.5秒

    while (1)
    {
        float temperature = 0.0;
        float pressure = 0.0;
        
        // Read MS5837 sensor data
        esp_err_t ret = ms5837_get_data(&sensor, &temperature, &pressure);
        
        if (ret == ESP_OK)
        {
            ESP_LOGI(TAG, "MS5837 Pressure: %.2f kPa, Temperature: %.2f deg", pressure, temperature);
        }
        else
        {
            ESP_LOGE(TAG, "MS5837 read failed: %s", esp_err_to_name(ret));
        }
        
        // Wait for 3 second
        vTaskDelay(pdMS_TO_TICKS(3000));
    }
}

esp_err_t sensor_test_init(void)
{
    ESP_LOGI(TAG, "Initializing sensor test module");
    
    // Initialize MS5837 sensor
    esp_err_t ret = ms5837_init(&sensor);
    if (ret != ESP_OK)
    {
        ESP_LOGE(TAG, "MS5837 sensor initialization failed: %s", esp_err_to_name(ret));
        return ret;
    }
    
    ESP_LOGI(TAG, "MS5837 sensor initialized successfully");
    return ESP_OK;
}

esp_err_t sensor_test_start_task(void)
{
    if (sensor_test_task_handle != NULL)
    {
        ESP_LOGW(TAG, "Sensor test task is already running");
        return ESP_OK;
    }
    
    // Create sensor test task
    BaseType_t ret = xTaskCreate(
        sensor_test_task,           // Task function
        "sensor_test",              // Task name
        4096,                       // Stack size
        NULL,                       // Task parameters
        5,                          // Task priority
        &sensor_test_task_handle    // Task handle
    );
    
    if (ret != pdPASS)
    {
        ESP_LOGE(TAG, "Failed to create sensor test task");
        return ESP_FAIL;
    }
    
    ESP_LOGI(TAG, "Sensor test task created successfully");
    return ESP_OK;
}

esp_err_t sensor_test_stop_task(void)
{
    if (sensor_test_task_handle == NULL)
    {
        ESP_LOGW(TAG, "Sensor test task is not running");
        return ESP_OK;
    }
    
    // Delete task
    vTaskDelete(sensor_test_task_handle);
    sensor_test_task_handle = NULL;
    
    ESP_LOGI(TAG, "Sensor test task stopped");
    return ESP_OK;
}
